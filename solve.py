#!/usr/bin/env python3

from Crypto.Cipher import AES
import os

def solve_nonceless_ctr():
    """
    Solve the nonceless AES-CTR challenge.

    The vulnerability: AES.new() in CTR mode without specifying a nonce
    uses a random nonce, but we can reproduce the exact same cipher
    by using the same key and no nonce parameter.
    """

    # Read the encrypted file
    with open('flag.png.enc', 'rb') as f:
        encrypted_data = f.read()

    print(f"Encrypted file size: {len(encrypted_data)} bytes")

    # PNG file header (first 8 bytes are always the same)
    png_header = b'\x89PNG\r\n\x1a\n'

    print("PNG header:", png_header.hex())
    print("Encrypted header:", encrypted_data[:8].hex())

    # Let's try to reproduce the exact same encryption process
    key = b'sup3rrr s3cr3ttt'

    # The challenge code: AES.new(b'sup3rrr s3cr3ttt', AES.MODE_CTR)
    # When no nonce is specified, PyCryptodome generates a random one
    # But the "thrown away" suggests it might be using a default/zero nonce

    # Let's try different approaches:
    approaches = [
        ("Zero nonce", b'\x00' * 8),
        ("Zero nonce 16 bytes", b'\x00' * 16),
        ("Empty nonce", b''),
    ]

    for name, nonce in approaches:
        print(f"\nTrying {name}: {nonce.hex() if nonce else 'empty'}")

        try:
            if nonce:
                cipher = AES.new(key, AES.MODE_CTR, nonce=nonce)
            else:
                # This might not work, but let's try
                continue

            # Generate keystream by encrypting zeros
            keystream = cipher.encrypt(b'\x00' * len(encrypted_data))

            # Decrypt by XORing with keystream
            decrypted = bytes(a ^ b for a, b in zip(encrypted_data, keystream))

            print(f"Decrypted header: {decrypted[:8].hex()}")

            # Check if it's a valid PNG
            if decrypted.startswith(png_header):
                print(f"✓ Success with {name}!")

                # Save the decrypted file
                with open('flag.png', 'wb') as f:
                    f.write(decrypted)

                print("✓ Saved decrypted file as 'flag.png'")
                return True

        except Exception as e:
            print(f"Error with {name}: {e}")

    # If standard approaches don't work, let's try the known-plaintext attack
    print("\nTrying known-plaintext attack...")
    return known_plaintext_attack(encrypted_data, png_header, key)

def known_plaintext_attack(encrypted_data, png_header, key):
    """
    Use known plaintext attack to recover the keystream.
    """
    # Since we know the PNG header, we can recover part of the keystream
    keystream_start = bytes(a ^ b for a, b in zip(encrypted_data[:8], png_header))
    print(f"Recovered keystream (first 8 bytes): {keystream_start.hex()}")

    # Try to find the nonce that produces this keystream
    # CTR mode: keystream = AES_encrypt(nonce || counter)

    # Try different nonce lengths and values
    for nonce_len in [8, 12, 16]:
        print(f"\nTrying nonce length: {nonce_len}")

        # Try some common nonce values
        test_nonces = [
            b'\x00' * nonce_len,
            b'\x01' * nonce_len,
            b'\xff' * nonce_len,
        ]

        # Also try incrementing nonces
        for i in range(256):
            test_nonces.append(i.to_bytes(nonce_len, 'big'))
            test_nonces.append(i.to_bytes(nonce_len, 'little'))

        for nonce in test_nonces:
            try:
                cipher = AES.new(key, AES.MODE_CTR, nonce=nonce)
                test_keystream = cipher.encrypt(b'\x00' * 8)

                if test_keystream == keystream_start:
                    print(f"✓ Found matching nonce: {nonce.hex()}")

                    # Generate full keystream
                    cipher = AES.new(key, AES.MODE_CTR, nonce=nonce)
                    full_keystream = cipher.encrypt(b'\x00' * len(encrypted_data))

                    # Decrypt
                    decrypted = bytes(a ^ b for a, b in zip(encrypted_data, full_keystream))

                    if decrypted.startswith(png_header):
                        with open('flag.png', 'wb') as f:
                            f.write(decrypted)
                        print("✓ Successfully decrypted and saved flag.png")
                        return True

            except Exception:
                continue

    print("✗ Could not find the correct nonce")
    return False

if __name__ == "__main__":
    print("=== Nonceless AES-CTR Challenge Solver ===")
    print()
    
    success = solve_nonceless_ctr()
    
    if success:
        print("\n🎉 Challenge solved! Check flag.png for the flag.")
    else:
        print("\n❌ Failed to solve the challenge.")
